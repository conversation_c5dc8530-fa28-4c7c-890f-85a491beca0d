
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.AdminsScalarFieldEnum = {
  admin_id: 'admin_id',
  name: 'name',
  created_at: 'created_at',
  account_id: 'account_id'
};

exports.Prisma.ArtistsScalarFieldEnum = {
  artist_id: 'artist_id',
  name: 'name',
  bio: 'bio',
  image: 'image'
};

exports.Prisma.CartScalarFieldEnum = {
  cart_id: 'cart_id',
  user_id: 'user_id',
  ticket_type_id: 'ticket_type_id',
  quantity: 'quantity',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.CategorysalesScalarFieldEnum = {
  category_sales_id: 'category_sales_id',
  event_id: 'event_id',
  organizer_id: 'organizer_id',
  category_id: 'category_id',
  tickets_sold: 'tickets_sold',
  revenue: 'revenue',
  last_updated: 'last_updated'
};

exports.Prisma.DailysalesScalarFieldEnum = {
  daily_sales_id: 'daily_sales_id',
  event_id: 'event_id',
  organizer_id: 'organizer_id',
  sale_date: 'sale_date',
  tickets_sold: 'tickets_sold',
  revenue: 'revenue'
};

exports.Prisma.EmailremindersScalarFieldEnum = {
  reminder_id: 'reminder_id',
  user_id: 'user_id',
  event_id: 'event_id',
  email_sent: 'email_sent',
  scheduled_date: 'scheduled_date',
  mail_text: 'mail_text',
  created_at: 'created_at'
};

exports.Prisma.EventartistsScalarFieldEnum = {
  event_id: 'event_id',
  artist_id: 'artist_id'
};

exports.Prisma.EventcategoriesScalarFieldEnum = {
  category_id: 'category_id',
  event_id: 'event_id',
  name: 'name',
  description: 'description',
  category_type: 'category_type'
};

exports.Prisma.EventsScalarFieldEnum = {
  event_id: 'event_id',
  organizer_id: 'organizer_id',
  title: 'title',
  description: 'description',
  start_date: 'start_date',
  end_date: 'end_date',
  start_time: 'start_time',
  banner_image: 'banner_image',
  venue_name: 'venue_name',
  location_id: 'location_id',
  genre_id: 'genre_id',
  tickets_sale_start: 'tickets_sale_start',
  tickets_sale_end: 'tickets_sale_end',
  event_policy: 'event_policy',
  status: 'status',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.EventsalesScalarFieldEnum = {
  event_sales_id: 'event_sales_id',
  event_id: 'event_id',
  organizer_id: 'organizer_id',
  tickets_sold: 'tickets_sold',
  total_revenue: 'total_revenue',
  last_updated: 'last_updated'
};

exports.Prisma.GenresScalarFieldEnum = {
  genre_id: 'genre_id',
  name: 'name',
  icon: 'icon'
};

exports.Prisma.LocationsScalarFieldEnum = {
  location_id: 'location_id',
  city: 'city',
  venue_name: 'venue_name',
  address: 'address',
  map_link: 'map_link'
};

exports.Prisma.MasteraccountsScalarFieldEnum = {
  account_id: 'account_id',
  email: 'email',
  role_type: 'role_type',
  role_id: 'role_id',
  oauth_id: 'oauth_id',
  created_at: 'created_at',
  email_verified: 'email_verified',
  updated_at: 'updated_at',
  last_login: 'last_login',
  oauth_provider: 'oauth_provider',
  password_hash: 'password_hash',
  reset_token: 'reset_token',
  reset_token_expires: 'reset_token_expires',
  verification_token: 'verification_token'
};

exports.Prisma.NotificationsScalarFieldEnum = {
  notification_id: 'notification_id',
  sender_id: 'sender_id',
  sender_type: 'sender_type',
  recipient_id: 'recipient_id',
  event_id: 'event_id',
  title: 'title',
  message: 'message',
  is_read: 'is_read',
  created_at: 'created_at'
};

exports.Prisma.OrderitemsScalarFieldEnum = {
  order_item_id: 'order_item_id',
  order_id: 'order_id',
  ticket_type_id: 'ticket_type_id',
  quantity: 'quantity',
  unit_price: 'unit_price'
};

exports.Prisma.OrdersScalarFieldEnum = {
  order_id: 'order_id',
  user_id: 'user_id',
  total_amount: 'total_amount',
  additional_fees: 'additional_fees',
  payment_status: 'payment_status',
  payment_method: 'payment_method',
  transaction_id: 'transaction_id',
  created_at: 'created_at'
};

exports.Prisma.OrganizersScalarFieldEnum = {
  organizer_id: 'organizer_id',
  organization_name: 'organization_name',
  phone_number: 'phone_number',
  logo: 'logo',
  description: 'description',
  facebook_link: 'facebook_link',
  insta_link: 'insta_link',
  web_link: 'web_link',
  status: 'status',
  created_at: 'created_at',
  updated_at: 'updated_at',
  account_id: 'account_id'
};

exports.Prisma.OrganizersalesScalarFieldEnum = {
  organizer_sales_id: 'organizer_sales_id',
  organizer_id: 'organizer_id',
  total_events: 'total_events',
  active_events: 'active_events',
  total_tickets_sold: 'total_tickets_sold',
  total_revenue: 'total_revenue',
  last_updated: 'last_updated'
};

exports.Prisma.TicketsScalarFieldEnum = {
  ticket_id: 'ticket_id',
  order_id: 'order_id',
  ticket_type_id: 'ticket_type_id',
  qr_code: 'qr_code',
  is_validated: 'is_validated',
  validation_time: 'validation_time',
  created_at: 'created_at',
  user_ticketpdf: 'user_ticketpdf',
  attendee_email: 'attendee_email',
  attendee_name: 'attendee_name',
  attendee_phone: 'attendee_phone'
};

exports.Prisma.TickettypesScalarFieldEnum = {
  ticket_type_id: 'ticket_type_id',
  event_id: 'event_id',
  category_id: 'category_id',
  name: 'name',
  description: 'description',
  price: 'price',
  quantity_available: 'quantity_available',
  max_per_order: 'max_per_order',
  banner: 'banner',
  pdf_template: 'pdf_template'
};

exports.Prisma.TickettypesalesScalarFieldEnum = {
  ticket_type_sales_id: 'ticket_type_sales_id',
  event_id: 'event_id',
  organizer_id: 'organizer_id',
  ticket_type_id: 'ticket_type_id',
  tickets_sold: 'tickets_sold',
  revenue: 'revenue',
  last_updated: 'last_updated'
};

exports.Prisma.UsersScalarFieldEnum = {
  user_id: 'user_id',
  first_name: 'first_name',
  last_name: 'last_name',
  phone_number: 'phone_number',
  profile_image: 'profile_image',
  gender: 'gender',
  dob: 'dob',
  created_at: 'created_at',
  updated_at: 'updated_at',
  account_id: 'account_id'
};

exports.Prisma.WishlistsScalarFieldEnum = {
  wishlist_id: 'wishlist_id',
  user_id: 'user_id',
  event_id: 'event_id',
  created_at: 'created_at'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.event_status_type = exports.$Enums.event_status_type = {
  draft: 'draft',
  pending: 'pending',
  approved: 'approved',
  live: 'live',
  completed: 'completed',
  cancelled: 'cancelled'
};

exports.role_type = exports.$Enums.role_type = {
  user: 'user',
  organizer: 'organizer',
  admin: 'admin'
};

exports.sender_type_enum = exports.$Enums.sender_type_enum = {
  system: 'system',
  organizer: 'organizer'
};

exports.payment_status_type = exports.$Enums.payment_status_type = {
  pending: 'pending',
  completed: 'completed',
  failed: 'failed',
  refunded: 'refunded'
};

exports.organizer_status_type = exports.$Enums.organizer_status_type = {
  pending: 'pending',
  approved: 'approved',
  rejected: 'rejected'
};

exports.Prisma.ModelName = {
  admins: 'admins',
  artists: 'artists',
  cart: 'cart',
  categorysales: 'categorysales',
  dailysales: 'dailysales',
  emailreminders: 'emailreminders',
  eventartists: 'eventartists',
  eventcategories: 'eventcategories',
  events: 'events',
  eventsales: 'eventsales',
  genres: 'genres',
  locations: 'locations',
  masteraccounts: 'masteraccounts',
  notifications: 'notifications',
  orderitems: 'orderitems',
  orders: 'orders',
  organizers: 'organizers',
  organizersales: 'organizersales',
  tickets: 'tickets',
  tickettypes: 'tickettypes',
  tickettypesales: 'tickettypesales',
  users: 'users',
  wishlists: 'wishlists'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
